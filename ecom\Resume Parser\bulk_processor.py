"""
Bulk Resume Processing System
Handles batch processing of multiple resumes with progress tracking and error handling
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from datetime import datetime

from resume_parser import ResumeParser
from scoring_engine import ScoringEngine
from config import RESUMES_DIR, SUPPORTED_FORMATS, create_directories, DEFAULT_THREAD_POOL_SIZE

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BulkProcessor:
    """Main class for bulk processing of resumes with multi-threading support"""

    def __init__(self, max_workers: int = DEFAULT_THREAD_POOL_SIZE, use_threading: bool = True):
        """
        Initialize bulk processor

        Args:
            max_workers (int): Maximum number of concurrent workers for processing
            use_threading (bool): Whether to use multi-threading for LLM calls
        """
        self.resume_parser = ResumeParser()
        self.scoring_engine = ScoringEngine(thread_pool_size=max_workers if use_threading else 1)
        self.max_workers = max_workers
        self.use_threading = use_threading

        # Ensure directories exist
        create_directories()
    
    def process_resumes_directory(self, 
                                 resumes_dir: str, 
                                 job_description: str,
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Process all resumes in a directory
        
        Args:
            resumes_dir (str): Path to directory containing resumes
            job_description (str): Job description to match against
            progress_callback (Callable): Optional callback for progress updates
            
        Returns:
            Dict containing processing results and statistics
        """
        
        resumes_path = Path(resumes_dir)
        
        if not resumes_path.exists():
            return {
                'success': False,
                'error': f'Directory does not exist: {resumes_dir}',
                'results': [],
                'statistics': {}
            }
        
        # Find all resume files
        resume_files = self._find_resume_files(resumes_path)
        
        if not resume_files:
            return {
                'success': False,
                'error': f'No supported resume files found in {resumes_dir}',
                'results': [],
                'statistics': {}
            }
        
        logger.info(f"Found {len(resume_files)} resume files to process")
        
        # Process resumes
        start_time = time.time()
        results = self._process_resume_files(resume_files, job_description, progress_callback)
        processing_time = time.time() - start_time
        
        # Calculate statistics
        statistics = self._calculate_statistics(results, processing_time)
        
        return {
            'success': True,
            'results': results,
            'statistics': statistics,
            'job_description': job_description,
            'processed_at': datetime.now().isoformat()
        }
    
    def process_resume_files(self, 
                           file_paths: List[str], 
                           job_description: str,
                           progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Process specific resume files
        
        Args:
            file_paths (List[str]): List of resume file paths
            job_description (str): Job description to match against
            progress_callback (Callable): Optional callback for progress updates
            
        Returns:
            Dict containing processing results and statistics
        """
        
        # Validate file paths
        valid_files = []
        for file_path in file_paths:
            path = Path(file_path)
            if path.exists() and path.suffix.lower() in SUPPORTED_FORMATS:
                valid_files.append(path)
            else:
                logger.warning(f"Skipping invalid file: {file_path}")
        
        if not valid_files:
            return {
                'success': False,
                'error': 'No valid resume files provided',
                'results': [],
                'statistics': {}
            }
        
        logger.info(f"Processing {len(valid_files)} resume files")
        
        # Process resumes
        start_time = time.time()
        results = self._process_resume_files(valid_files, job_description, progress_callback)
        processing_time = time.time() - start_time
        
        # Calculate statistics
        statistics = self._calculate_statistics(results, processing_time)
        
        return {
            'success': True,
            'results': results,
            'statistics': statistics,
            'job_description': job_description,
            'processed_at': datetime.now().isoformat()
        }
    
    def _find_resume_files(self, directory: Path) -> List[Path]:
        """Find all supported resume files in directory"""
        
        resume_files = []
        
        for file_path in directory.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix.lower() in SUPPORTED_FORMATS and
                not file_path.name.startswith('.')):  # Skip hidden files
                resume_files.append(file_path)
        
        return sorted(resume_files)
    
    def _process_resume_files(self,
                            file_paths: List[Path],
                            job_description: str,
                            progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Process resume files with optimized multi-threading"""

        total_files = len(file_paths)
        logger.info(f"Processing {total_files} resumes using {'multi-threading' if self.use_threading else 'sequential'} approach")

        if not self.use_threading or self.max_workers == 1:
            # Sequential processing
            return self._process_sequential(file_paths, job_description, progress_callback)
        else:
            # Multi-threaded processing with optimized approach
            return self._process_multithreaded(file_paths, job_description, progress_callback)

    def _process_sequential(self, file_paths: List[Path], job_description: str,
                          progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Process resumes sequentially"""
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            result = self._process_single_resume(file_path, job_description)
            results.append(result)

            if progress_callback:
                progress_callback(i + 1, total_files, result)

            logger.info(f"Processed {i + 1}/{total_files}: {file_path.name}")

        return results

    def _process_multithreaded(self, file_paths: List[Path], job_description: str,
                             progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Process resumes using multi-threading with batch LLM calls"""
        total_files = len(file_paths)
        results = []

        # Step 1: Parse all resumes first (I/O bound, can be parallelized)
        logger.info("Step 1: Parsing resume files...")
        parsed_resumes = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(self._parse_resume_file, file_path): file_path
                for file_path in file_paths
            }

            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    parsed_resume = future.result()
                    parsed_resumes.append((parsed_resume, file_path))
                except Exception as e:
                    logger.error(f"Error parsing {file_path.name}: {str(e)}")
                    error_resume = {
                        'success': False,
                        'filename': file_path.name,
                        'error': str(e),
                        'text': '',
                        'metadata': {}
                    }
                    parsed_resumes.append((error_resume, file_path))

        # Step 2: Batch process with LLM using optimized threading
        logger.info("Step 2: Analyzing resumes with LLM...")

        # Prepare data for batch processing
        valid_resumes = [(resume, file_path) for resume, file_path in parsed_resumes if resume['success']]
        failed_resumes = [(resume, file_path) for resume, file_path in parsed_resumes if not resume['success']]

        # Process valid resumes in batches
        if valid_resumes:
            batch_results = self.scoring_engine.batch_score_resumes_threaded(
                [resume for resume, _ in valid_resumes],
                job_description,
                progress_callback
            )

            # Combine results
            for i, (result, (_, file_path)) in enumerate(zip(batch_results, valid_resumes)):
                result['file_path'] = str(file_path)
                results.append(result)

        # Add failed parsing results
        for resume, file_path in failed_resumes:
            result = {
                'success': False,
                'filename': resume['filename'],
                'error': resume['error'],
                'final_score': 0,
                'recommendation': 'REJECT',
                'file_path': str(file_path)
            }
            results.append(result)

        return results

    def _parse_resume_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse a single resume file (for threading)"""
        return self.resume_parser.parse_resume(str(file_path))
    
    def _process_single_resume(self, file_path: Path, job_description: str) -> Dict[str, Any]:
        """Process a single resume file"""
        
        try:
            # Parse resume
            parsed_resume = self.resume_parser.parse_resume(str(file_path))
            
            if not parsed_resume['success']:
                return {
                    'success': False,
                    'filename': file_path.name,
                    'error': parsed_resume['error'],
                    'final_score': 0,
                    'recommendation': 'REJECT'
                }
            
            # Score resume
            scoring_result = self.scoring_engine.score_resume(parsed_resume, job_description)
            
            # Add file path information
            scoring_result['file_path'] = str(file_path)
            
            return scoring_result
            
        except Exception as e:
            logger.error(f"Unexpected error processing {file_path.name}: {str(e)}")
            return {
                'success': False,
                'filename': file_path.name,
                'error': str(e),
                'final_score': 0,
                'recommendation': 'REJECT',
                'file_path': str(file_path)
            }
    
    def _calculate_statistics(self, results: List[Dict[str, Any]], processing_time: float) -> Dict[str, Any]:
        """Calculate processing statistics"""
        
        total_resumes = len(results)
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]
        
        if not successful_results:
            return {
                'total_resumes': total_resumes,
                'successful': 0,
                'failed': len(failed_results),
                'processing_time': processing_time,
                'average_score': 0,
                'recommendations': {'HIRE': 0, 'CONSIDER': 0, 'REJECT': total_resumes}
            }
        
        # Score statistics
        scores = [r.get('final_score', 0) for r in successful_results]
        average_score = sum(scores) / len(scores) if scores else 0
        max_score = max(scores) if scores else 0
        min_score = min(scores) if scores else 0
        
        # Recommendation statistics
        recommendations = {'HIRE': 0, 'CONSIDER': 0, 'REJECT': 0}
        for result in results:
            rec = result.get('recommendation', 'REJECT')
            recommendations[rec] = recommendations.get(rec, 0) + 1
        
        # Top candidates
        top_candidates = sorted(
            successful_results, 
            key=lambda x: x.get('final_score', 0), 
            reverse=True
        )[:5]
        
        return {
            'total_resumes': total_resumes,
            'successful': len(successful_results),
            'failed': len(failed_results),
            'processing_time': round(processing_time, 2),
            'average_processing_time_per_resume': round(processing_time / total_resumes, 2),
            'average_score': round(average_score, 2),
            'max_score': round(max_score, 2),
            'min_score': round(min_score, 2),
            'recommendations': recommendations,
            'top_candidates': [
                {
                    'filename': candidate['filename'],
                    'score': candidate.get('final_score', 0),
                    'recommendation': candidate.get('recommendation', 'REJECT')
                }
                for candidate in top_candidates
            ]
        }


def test_bulk_processor():
    """Test function for bulk processor"""
    
    # Create test directory and files
    test_dir = Path("test_resumes")
    test_dir.mkdir(exist_ok=True)
    
    # Create sample resume files
    sample_resumes = [
        ("john_doe.txt", """John Doe
Software Developer
<EMAIL>
3 years Python, React, SQL experience
Bachelor's Computer Science"""),
        
        ("jane_smith.txt", """Jane Smith
Data Scientist
<EMAIL>
5 years Python, Machine Learning, Analytics
PhD in Statistics"""),
        
        ("bob_wilson.txt", """Bob Wilson
Frontend Developer
<EMAIL>
2 years JavaScript, React, CSS
Associate's degree""")
    ]
    
    for filename, content in sample_resumes:
        with open(test_dir / filename, 'w') as f:
            f.write(content)
    
    # Test processing
    processor = BulkProcessor(max_workers=1)
    
    job_desc = """
    Software Developer Position
    Required: Python, JavaScript, React, SQL
    Experience: 2-5 years
    Education: Bachelor's degree preferred
    """
    
    def progress_callback(current, total, result):
        print(f"Progress: {current}/{total} - {result.get('filename', 'unknown')}")
    
    results = processor.process_resumes_directory(
        str(test_dir), 
        job_desc, 
        progress_callback
    )
    
    print("\nTest Results:")
    print(f"Success: {results['success']}")
    print(f"Statistics: {results.get('statistics', {})}")
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir)


if __name__ == "__main__":
    test_bulk_processor()
