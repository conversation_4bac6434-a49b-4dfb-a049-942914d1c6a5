# Resume Parser - Django Integration Ready

This resume parser has been simplified and optimized for Django integration by removing all multi-threading components and providing a clean, sequential processing interface.

## Changes Made

### 1. Removed Multi-threading Components
- Removed `ThreadPoolExecutor` and `concurrent.futures` imports
- Removed threading-related configuration constants
- Simplified `OllamaClient` to use sequential processing only
- Removed `batch_score_resumes_threaded` method from `ScoringEngine`
- Simplified `BulkProcessor` to use only sequential processing
- Removed threading CLI options from `main.py`

### 2. Added Django-Ready Interface
- Created `django_interface.py` with `ResumeAnalyzer` class
- Provides simple methods for single and multiple resume analysis
- Includes utility methods for quick scoring and recommendations
- Handles file paths and text content directly

## Django Integration

### Basic Usage

```python
from resume_parser.django_interface import ResumeAnalyzer

# Initialize analyzer (can be reused across requests)
analyzer = ResumeAnalyzer()

# Analyze a single resume file
job_description = "Software Developer with Python and React experience"
result = analyzer.analyze_single_resume("/path/to/resume.pdf", job_description)

if result['success']:
    score = result['final_score']
    recommendation = result['recommendation']
    analysis = result['analysis']
```

### Django View Example

```python
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from .resume_parser.django_interface import ResumeAnalyzer

# Initialize analyzer once (consider using Django cache or singleton)
analyzer = ResumeAnalyzer()

@csrf_exempt
@require_http_methods(["POST"])
def analyze_resume(request):
    try:
        data = json.loads(request.body)
        resume_text = data.get('resume_text')
        job_description = data.get('job_description')
        filename = data.get('filename', 'uploaded_resume')
        
        if not resume_text or not job_description:
            return JsonResponse({'error': 'Missing resume_text or job_description'}, status=400)
        
        result = analyzer.analyze_resume_content(resume_text, filename, job_description)
        
        return JsonResponse({
            'success': result['success'],
            'score': result.get('final_score', 0),
            'recommendation': result.get('recommendation', 'REJECT'),
            'analysis': result.get('analysis', {}),
            'strengths': result.get('strengths', []),
            'weaknesses': result.get('weaknesses', [])
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def quick_score(request):
    try:
        data = json.loads(request.body)
        resume_text = data.get('resume_text')
        job_description = data.get('job_description')
        
        if not resume_text or not job_description:
            return JsonResponse({'error': 'Missing resume_text or job_description'}, status=400)
        
        score = analyzer.get_quick_score(resume_text, job_description)
        recommendation = analyzer.get_recommendation(resume_text, job_description)
        
        return JsonResponse({
            'score': score,
            'recommendation': recommendation
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
```

### Django Model Integration

```python
from django.db import models
from .resume_parser.django_interface import ResumeAnalyzer

class JobPosting(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

class ResumeSubmission(models.Model):
    job_posting = models.ForeignKey(JobPosting, on_delete=models.CASCADE)
    candidate_name = models.CharField(max_length=100)
    resume_file = models.FileField(upload_to='resumes/')
    resume_text = models.TextField()
    score = models.FloatField(null=True, blank=True)
    recommendation = models.CharField(max_length=20, null=True, blank=True)
    analysis_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def analyze_resume(self):
        """Analyze this resume against the job posting"""
        analyzer = ResumeAnalyzer()
        result = analyzer.analyze_resume_content(
            self.resume_text, 
            self.resume_file.name, 
            self.job_posting.description
        )
        
        if result['success']:
            self.score = result['final_score']
            self.recommendation = result['recommendation']
            self.analysis_data = {
                'analysis': result.get('analysis', {}),
                'strengths': result.get('strengths', []),
                'weaknesses': result.get('weaknesses', []),
                'scores': result.get('scores', {})
            }
            self.save()
        
        return result
```

### Celery Integration (for background processing)

```python
from celery import shared_task
from .models import ResumeSubmission
from .resume_parser.django_interface import ResumeAnalyzer

@shared_task
def analyze_resume_async(submission_id):
    """Analyze resume in background using Celery"""
    try:
        submission = ResumeSubmission.objects.get(id=submission_id)
        analyzer = ResumeAnalyzer()
        
        result = analyzer.analyze_resume_content(
            submission.resume_text,
            submission.resume_file.name,
            submission.job_posting.description
        )
        
        if result['success']:
            submission.score = result['final_score']
            submission.recommendation = result['recommendation']
            submission.analysis_data = result
            submission.save()
            
        return {'success': True, 'submission_id': submission_id}
        
    except Exception as e:
        return {'success': False, 'error': str(e)}
```

## Key Features

### ResumeAnalyzer Methods

1. **`analyze_single_resume(file_path, job_description)`** - Analyze a resume file
2. **`analyze_resume_content(text, filename, job_description)`** - Analyze resume text directly
3. **`analyze_multiple_resumes(file_list, job_description)`** - Batch process multiple resumes
4. **`get_quick_score(text, job_description)`** - Get just the score (0-100)
5. **`get_recommendation(text, job_description)`** - Get just the recommendation (HIRE/CONSIDER/REJECT)
6. **`export_results_to_json(results)`** - Export results to JSON format

### Benefits for Django

- **No Threading Complexity**: Sequential processing eliminates race conditions and threading issues
- **Simple Integration**: Clean interface that works well with Django's request-response cycle
- **Memory Efficient**: No thread pools or concurrent processing overhead
- **Error Handling**: Comprehensive error handling suitable for web applications
- **Flexible Input**: Supports both file paths and text content
- **JSON Export**: Built-in JSON export for API responses

## Requirements

- Python 3.8+
- Ollama with llama3.2:3b model
- All original dependencies (see requirements.txt)

## Installation in Django Project

1. Copy the `Resume Parser` folder to your Django project
2. Install required dependencies: `pip install -r requirements.txt`
3. Ensure Ollama is running: `ollama serve`
4. Import and use `ResumeAnalyzer` in your views

## Performance Considerations

- Sequential processing is slower than multi-threading but more predictable
- Consider using Celery for background processing of large batches
- Cache the `ResumeAnalyzer` instance to avoid repeated initialization
- Use database caching for frequently analyzed job descriptions

## Testing

The simplified implementation has been tested to ensure:
- All imports work correctly
- CLI interface functions without threading options
- Components integrate properly
- Error handling works as expected

Run tests with:
```bash
python -c "from django_interface import ResumeAnalyzer; print('Success')"
python main.py --help
```
