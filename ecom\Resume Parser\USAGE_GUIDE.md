# Resume Parser - Simple Usage Guide

The resume parser has been simplified to provide just two main functions for easy integration into your Django project.

## Available Functions

### 1. `process_directory()` - Process All Resumes in a Directory

```python
from main import process_directory

# Process all resumes in a directory
results = process_directory(
    resumes_dir="path/to/resumes",           # Required: Directory containing resume files
    job_description="Your job description",  # Required: Job description text
    output_dir="path/to/output",            # Optional: Output directory (default: ./output)
    min_score=60,                           # Optional: Minimum score filter (default: 0)
    verbose=True                            # Optional: Enable verbose logging (default: False)
)

# Access results
print(f"Total resumes processed: {results['statistics']['total_resumes']}")
print(f"Average score: {results['statistics']['average_score']}")
print(f"Recommendations: {results['statistics']['recommendations']}")

# Access individual results
for resume_result in results['results']:
    print(f"File: {resume_result['filename']}")
    print(f"Score: {resume_result['final_score']}")
    print(f"Recommendation: {resume_result['recommendation']}")
```

### 2. `process_single_resume()` - Process One Resume File

```python
from main import process_single_resume

# Process a single resume
result = process_single_resume(
    resume_file_path="path/to/resume.pdf",   # Required: Path to resume file
    job_description="Your job description",  # Required: Job description text
    output_dir="path/to/output",            # Optional: Output directory (default: ./output)
    verbose=True                            # Optional: Enable verbose logging (default: False)
)

# Check if successful
if result['success']:
    print(f"Score: {result['final_score']}")
    print(f"Recommendation: {result['recommendation']}")
    print(f"Strengths: {result['strengths']}")
    print(f"Weaknesses: {result['weaknesses']}")
else:
    print(f"Error: {result['error']}")
```

### 3. `setup_environment()` - Test System Setup

```python
from main import setup_environment

# Test if everything is set up correctly
if setup_environment():
    print("System is ready!")
else:
    print("Setup failed - check Ollama installation")
```

## Quick Start Examples

### Example 1: Basic Directory Processing

```python
from main import process_directory

job_desc = """
Software Developer Position
Required: Python, JavaScript, React, SQL
Experience: 2-5 years
Education: Bachelor's degree preferred
"""

# Process all resumes in the 'resumes' folder
results = process_directory("resumes", job_desc, min_score=70)

# Show top candidates
for candidate in results['statistics'].get('top_candidates', []):
    print(f"{candidate['filename']}: {candidate['score']:.1f} - {candidate['recommendation']}")
```

### Example 2: Single Resume Analysis

```python
from main import process_single_resume

job_desc = "Data Scientist with Python and Machine Learning experience"

# Analyze one specific resume
result = process_single_resume("resumes/john_doe.pdf", job_desc, verbose=True)

if result['success']:
    print(f"Candidate: {result['metadata']['candidate_name']}")
    print(f"Final Score: {result['final_score']}/100")
    print(f"Recommendation: {result['recommendation']}")
    
    # Detailed analysis
    analysis = result['analysis']
    print(f"Skills Match: {analysis['skills_match']:.1f}%")
    print(f"Experience Relevance: {analysis['experience_relevance']:.1f}%")
    print(f"Matching Skills: {', '.join(analysis['matching_skills'])}")
    print(f"Missing Skills: {', '.join(analysis['missing_skills'])}")
```

## Django Integration Examples

### Django View Function

```python
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
from .resume_parser.main import process_single_resume

@csrf_exempt
def analyze_uploaded_resume(request):
    if request.method == 'POST':
        try:
            # Get uploaded file and job description
            resume_file = request.FILES['resume']
            job_description = request.POST['job_description']
            
            # Save uploaded file temporarily
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                for chunk in resume_file.chunks():
                    tmp_file.write(chunk)
                tmp_path = tmp_file.name
            
            # Analyze the resume
            result = process_single_resume(tmp_path, job_description)
            
            # Clean up temp file
            import os
            os.unlink(tmp_path)
            
            # Return results
            if result['success']:
                return JsonResponse({
                    'success': True,
                    'score': result['final_score'],
                    'recommendation': result['recommendation'],
                    'analysis': result['analysis']
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': result['error']
                }, status=400)
                
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)
```

### Django Management Command

```python
# management/commands/analyze_resumes.py
from django.core.management.base import BaseCommand
from ...resume_parser.main import process_directory

class Command(BaseCommand):
    help = 'Analyze all resumes in a directory'
    
    def add_arguments(self, parser):
        parser.add_argument('resumes_dir', type=str, help='Directory containing resumes')
        parser.add_argument('job_description', type=str, help='Job description text')
        parser.add_argument('--min-score', type=float, default=0, help='Minimum score filter')
    
    def handle(self, *args, **options):
        self.stdout.write('Starting resume analysis...')
        
        results = process_directory(
            resumes_dir=options['resumes_dir'],
            job_description=options['job_description'],
            min_score=options['min_score'],
            verbose=True
        )
        
        stats = results['statistics']
        self.stdout.write(
            self.style.SUCCESS(
                f'Analysis complete! Processed {stats["total_resumes"]} resumes. '
                f'Average score: {stats["average_score"]:.2f}'
            )
        )
```

## Return Data Structure

### Directory Processing Results
```python
{
    'success': True,
    'results': [
        {
            'success': True,
            'filename': 'john_doe.pdf',
            'final_score': 85.5,
            'recommendation': 'HIRE',
            'analysis': {...},
            'strengths': [...],
            'weaknesses': [...],
            'metadata': {...}
        },
        # ... more results
    ],
    'statistics': {
        'total_resumes': 10,
        'successful': 9,
        'failed': 1,
        'average_score': 72.3,
        'recommendations': {
            'HIRE': 3,
            'CONSIDER': 4,
            'REJECT': 3
        }
    },
    'job_description': 'Original job description text'
}
```

### Single Resume Results
```python
{
    'success': True,
    'filename': 'john_doe.pdf',
    'final_score': 85.5,
    'recommendation': 'HIRE',
    'scores': {
        'skills_match': 90.0,
        'experience_relevance': 85.0,
        'education_match': 80.0,
        'keywords_match': 88.0,
        'overall_fit': 87.0
    },
    'analysis': {
        'candidate_name': 'John Doe',
        'skills_match': 90.0,
        'matching_skills': ['Python', 'JavaScript', 'React'],
        'missing_skills': ['AWS', 'Docker'],
        'summary': 'Strong candidate with relevant experience...'
    },
    'strengths': ['Strong technical skills', 'Relevant experience'],
    'weaknesses': ['Missing cloud experience'],
    'metadata': {
        'candidate_name': 'John Doe',
        'word_count': 450,
        'has_email': True,
        'has_phone': True
    }
}
```

## Requirements

1. **Ollama**: Must be running with llama3.2:3b model
   ```bash
   ollama serve
   ollama pull llama3.2:3b
   ```

2. **Python Dependencies**: Install from requirements.txt
   ```bash
   pip install -r requirements.txt
   ```

3. **Supported File Formats**: PDF, DOCX, TXT (configured in config.py)

## Error Handling

Both functions include comprehensive error handling:
- File not found errors
- Invalid job descriptions
- Ollama connection issues
- Resume parsing failures

Always check the `success` field in returned results before accessing other data.

## Performance Notes

- Sequential processing (no multi-threading)
- Processing time depends on resume length and Ollama response time
- Typical processing: 5-15 seconds per resume
- Results are automatically exported to JSON files in the output directory
