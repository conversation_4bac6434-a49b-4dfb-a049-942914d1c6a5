"""
Resume Parser and Scoring System - Main CLI Application
Bulk resume processing and scoring using Ollama LLM
"""

import sys
import click
import logging
from pathlib import Path
from typing import Optional

from bulk_processor import BulkProcessor
from export_utils import ExportUtils
from config import (
    RESUMES_DIR, OUTPUT_DIR, DEFAULT_JOB_DESCRIPTION,
    OUTPUT_FORMATS, DEFAULT_OUTPUT_FORMAT, create_directories
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.version_option(version='1.0.0')
def cli():
    """
    Resume Parser and Scoring System
    
    A Python tool for bulk processing and scoring resumes against job descriptions
    using Ollama LLM (llama3.2:3b).
    """
    # Ensure directories exist
    create_directories()


@cli.command()
@click.option('--job-description', '-j', 
              help='Job description text to match resumes against')
@click.option('--job-description-file', '-f', 
              type=click.Path(exists=True),
              help='Path to file containing job description')
@click.option('--resumes-dir', '-r', 
              default=str(RESUMES_DIR),
              type=click.Path(exists=True),
              help=f'Directory containing resume files (default: {RESUMES_DIR})')
@click.option('--output-dir', '-o', 
              default=str(OUTPUT_DIR),
              help=f'Output directory for results (default: {OUTPUT_DIR})')
@click.option('--format', 'output_format',
              default=DEFAULT_OUTPUT_FORMAT,
              type=click.Choice(OUTPUT_FORMATS + ['web-json']),
              help=f'Output format (default: {DEFAULT_OUTPUT_FORMAT}). Use "web-json" for web-optimized JSON')
@click.option('--filename', 
              help='Custom output filename (optional)')
@click.option('--min-score',
              type=float,
              default=0,
              help='Minimum score to include in results (default: 0)')
@click.option('--verbose', '-v',
              is_flag=True,
              help='Enable verbose logging')
def process(job_description: Optional[str],
           job_description_file: Optional[str],
           resumes_dir: str,
           output_dir: str,
           output_format: str,
           filename: Optional[str],
           min_score: float,
           verbose: bool):
    """Process resumes in a directory and generate scoring results."""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate job description input
    if not job_description and not job_description_file:
        click.echo("Error: Either --job-description or --job-description-file must be provided")
        click.echo("Using default job description for demo purposes...")
        job_description = DEFAULT_JOB_DESCRIPTION
    
    # Read job description from file if provided
    if job_description_file:
        try:
            with open(job_description_file, 'r', encoding='utf-8') as f:
                job_description = f.read().strip()
        except Exception as e:
            click.echo(f"Error reading job description file: {e}", err=True)
            sys.exit(1)
    
    # Validate inputs
    if not job_description.strip():
        click.echo("Error: Job description cannot be empty", err=True)
        sys.exit(1)
    
    # Setup output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    click.echo(f"🚀 Starting resume processing...")
    click.echo(f"📁 Resumes directory: {resumes_dir}")
    click.echo(f"📄 Job description: {job_description[:100]}...")
    click.echo(f"💾 Output format: {output_format}")
    click.echo(f"🔄 Processing: Sequential")
    
    # Progress callback
    def progress_callback(current: int, total: int, result: dict):
        filename = result.get('filename', 'unknown')
        score = result.get('final_score', 0)
        recommendation = result.get('recommendation', 'UNKNOWN')
        
        if result.get('success', False):
            click.echo(f"✅ [{current}/{total}] {filename} - Score: {score:.1f} - {recommendation}")
        else:
            error = result.get('error', 'Unknown error')
            click.echo(f"❌ [{current}/{total}] {filename} - Error: {error}")
    
    # Process resumes
    try:
        processor = BulkProcessor()
        results = processor.process_resumes_directory(
            resumes_dir,
            job_description,
            progress_callback
        )
        
        if not results['success']:
            click.echo(f"❌ Processing failed: {results['error']}", err=True)
            sys.exit(1)
        
        # Filter results by minimum score
        if min_score > 0:
            original_count = len(results['results'])
            results['results'] = [
                r for r in results['results'] 
                if r.get('final_score', 0) >= min_score
            ]
            filtered_count = len(results['results'])
            click.echo(f"🔍 Filtered results: {filtered_count}/{original_count} resumes with score >= {min_score}")
        
        # Export results
        exporter = ExportUtils()

        if output_format == 'web-json':
            # Export web-optimized JSON
            export_result = exporter.export_web_json(results, filename)
            if export_result['success']:
                click.echo(f"💾 Web-optimized JSON exported to: {export_result['file_path']}")
                click.echo(f"🌐 Ready for web integration with {export_result['records_exported']} candidates")
            else:
                click.echo(f"❌ Web JSON export failed: {export_result['error']}", err=True)
        else:
            # Standard export
            export_result = exporter.export_results(results, output_format, filename)
            if export_result['success']:
                click.echo(f"💾 Results exported to: {export_result['file_path']}")
            else:
                click.echo(f"❌ Export failed: {export_result['error']}", err=True)

        # Always create summary report (unless web-json format)
        if output_format != 'web-json':
            summary_result = exporter.export_summary_report(results)
            if summary_result['success']:
                click.echo(f"📊 Summary report: {summary_result['file_path']}")

        # Also create web JSON for API usage (if not already created)
        if output_format != 'web-json':
            web_json_result = exporter.export_web_json(results)
            if web_json_result['success']:
                click.echo(f"🌐 Web API JSON: {web_json_result['file_path']}")
        
        # Display summary
        stats = results['statistics']
        click.echo("\n📈 PROCESSING SUMMARY:")
        click.echo(f"   Total resumes: {stats['total_resumes']}")
        click.echo(f"   Successful: {stats['successful']}")
        click.echo(f"   Failed: {stats['failed']}")
        click.echo(f"   Average score: {stats['average_score']:.2f}")
        click.echo(f"   Processing time: {stats['processing_time']:.2f}s")
        
        click.echo("\n🎯 RECOMMENDATIONS:")
        recs = stats['recommendations']
        click.echo(f"   HIRE: {recs['HIRE']}")
        click.echo(f"   CONSIDER: {recs['CONSIDER']}")
        click.echo(f"   REJECT: {recs['REJECT']}")
        
        # Show top candidates
        top_candidates = stats.get('top_candidates', [])
        if top_candidates:
            click.echo("\n🏆 TOP CANDIDATES:")
            for i, candidate in enumerate(top_candidates[:3], 1):
                click.echo(f"   {i}. {candidate['filename']} - "
                          f"Score: {candidate['score']:.1f} - "
                          f"{candidate['recommendation']}")
        
        click.echo("\n✅ Processing completed successfully!")
        
    except KeyboardInterrupt:
        click.echo("\n⚠️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Unexpected error: {str(e)}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.argument('resume_files', nargs=-1, required=True, type=click.Path(exists=True))
@click.option('--job-description', '-j', 
              help='Job description text to match resumes against')
@click.option('--job-description-file', '-f', 
              type=click.Path(exists=True),
              help='Path to file containing job description')
@click.option('--output-dir', '-o', 
              default=str(OUTPUT_DIR),
              help=f'Output directory for results (default: {OUTPUT_DIR})')
@click.option('--format', 'output_format',
              default=DEFAULT_OUTPUT_FORMAT,
              type=click.Choice(OUTPUT_FORMATS + ['web-json']),
              help=f'Output format (default: {DEFAULT_OUTPUT_FORMAT}). Use "web-json" for web-optimized JSON')
@click.option('--verbose', '-v', 
              is_flag=True,
              help='Enable verbose logging')
def score(resume_files: tuple,
          job_description: Optional[str],
          job_description_file: Optional[str],
          output_dir: str,
          output_format: str,
          verbose: bool):
    """Score specific resume files."""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate job description input
    if not job_description and not job_description_file:
        click.echo("Error: Either --job-description or --job-description-file must be provided")
        sys.exit(1)
    
    # Read job description from file if provided
    if job_description_file:
        try:
            with open(job_description_file, 'r', encoding='utf-8') as f:
                job_description = f.read().strip()
        except Exception as e:
            click.echo(f"Error reading job description file: {e}", err=True)
            sys.exit(1)
    
    click.echo(f"🚀 Scoring {len(resume_files)} resume files...")
    
    # Progress callback
    def progress_callback(current: int, total: int, result: dict):
        filename = result.get('filename', 'unknown')
        score = result.get('final_score', 0)
        recommendation = result.get('recommendation', 'UNKNOWN')
        
        if result.get('success', False):
            click.echo(f"✅ [{current}/{total}] {filename} - Score: {score:.1f} - {recommendation}")
        else:
            error = result.get('error', 'Unknown error')
            click.echo(f"❌ [{current}/{total}] {filename} - Error: {error}")
    
    try:
        processor = BulkProcessor()  # Sequential processing
        results = processor.process_resume_files(
            list(resume_files), 
            job_description, 
            progress_callback
        )
        
        if not results['success']:
            click.echo(f"❌ Processing failed: {results['error']}", err=True)
            sys.exit(1)
        
        # Export results
        exporter = ExportUtils()

        if output_format == 'web-json':
            # Export web-optimized JSON
            export_result = exporter.export_web_json(results)
            if export_result['success']:
                click.echo(f"💾 Web-optimized JSON exported to: {export_result['file_path']}")
                click.echo(f"🌐 Ready for web integration with {export_result['records_exported']} candidates")
            else:
                click.echo(f"❌ Web JSON export failed: {export_result['error']}", err=True)
        else:
            # Standard export
            export_result = exporter.export_results(results, output_format)
            if export_result['success']:
                click.echo(f"💾 Results exported to: {export_result['file_path']}")
            else:
                click.echo(f"❌ Export failed: {export_result['error']}", err=True)
        
        click.echo("✅ Scoring completed!")
        
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
def setup():
    """Setup the resume parser environment and test Ollama connection."""
    
    click.echo("🔧 Setting up Resume Parser environment...")
    
    # Create directories
    create_directories()
    click.echo(f"✅ Created directories: {RESUMES_DIR}, {OUTPUT_DIR}")
    
    # Test Ollama connection
    try:
        from ollama_client import OllamaClient
        client = OllamaClient()
        click.echo("✅ Ollama connection successful")
        
        # Test with a simple prompt
        test_result = client.generate_response("Hello, are you working?")
        if test_result['success']:
            click.echo("✅ Ollama model test successful")
        else:
            click.echo(f"⚠️  Ollama model test failed: {test_result['error']}")
            
    except Exception as e:
        click.echo(f"❌ Ollama connection failed: {str(e)}")
        click.echo("Make sure Ollama is running: 'ollama serve'")
        click.echo("And the model is available: 'ollama pull llama3.2:3b'")
    
    click.echo("\n📋 Next steps:")
    click.echo(f"1. Place resume files in: {RESUMES_DIR}")
    click.echo("2. Run: python main.py process --job-description 'Your job description'")
    click.echo("3. Check results in: output/")


if __name__ == '__main__':
    cli()
