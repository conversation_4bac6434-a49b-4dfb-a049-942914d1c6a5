"""
Resume Parser and Scoring System - Simple Interface
Parse and score resumes using Ollama LLM
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any, List

from django_interface import ResumeAnalyzer
from export_utils import ExportUtils
from config import RESUMES_DIR, OUTPUT_DIR, create_directories

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def process_directory(resumes_dir: str, job_description: str,
                     output_dir: str = None, min_score: float = 0,
                     verbose: bool = False) -> Dict[str, Any]:
    """
    Process all resumes in a directory and score them against a job description

    Args:
        resumes_dir (str): Path to directory containing resume files
        job_description (str): Job description to match resumes against
        output_dir (str): Output directory for results (optional)
        min_score (float): Minimum score to include in results (default: 0)
        verbose (bool): Enable verbose logging

    Returns:
        Dict containing processing results and statistics
    """

    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Ensure directories exist
    create_directories()

    # Validate inputs
    if not job_description.strip():
        raise ValueError("Job description cannot be empty")

    if not Path(resumes_dir).exists():
        raise ValueError(f"Resumes directory does not exist: {resumes_dir}")

    # Setup output directory
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
    else:
        output_dir = OUTPUT_DIR
        Path(output_dir).mkdir(exist_ok=True)

    logger.info(f"🚀 Starting resume processing...")
    logger.info(f"📁 Resumes directory: {resumes_dir}")
    logger.info(f"📄 Job description: {job_description[:100]}...")
    logger.info(f"🔄 Processing: Sequential")

    # Progress callback
    def progress_callback(current: int, total: int, result: dict):
        filename = result.get('filename', 'unknown')
        score = result.get('final_score', 0)
        recommendation = result.get('recommendation', 'UNKNOWN')

        if result.get('success', False):
            logger.info(f"✅ [{current}/{total}] {filename} - Score: {score:.1f} - {recommendation}")
        else:
            error = result.get('error', 'Unknown error')
            logger.error(f"❌ [{current}/{total}] {filename} - Error: {error}")

    # Process resumes using ResumeAnalyzer
    analyzer = ResumeAnalyzer()

    # Find all resume files
    resume_files = []
    resumes_path = Path(resumes_dir)

    # Supported formats from config
    from config import SUPPORTED_FORMATS

    for file_path in resumes_path.rglob('*'):
        if (file_path.is_file() and
            file_path.suffix.lower() in SUPPORTED_FORMATS and
            not file_path.name.startswith('.')):  # Skip hidden files
            resume_files.append(str(file_path))

    if not resume_files:
        raise ValueError(f"No supported resume files found in {resumes_dir}")

    # Process resumes
    results = analyzer.analyze_multiple_resumes(resume_files, job_description, progress_callback)

    if not results['success']:
        raise RuntimeError(f"Processing failed: {results.get('error', 'Unknown error')}")

    # Filter results by minimum score
    if min_score > 0:
        original_count = len(results['results'])
        results['results'] = [
            r for r in results['results']
            if r.get('final_score', 0) >= min_score
        ]
        filtered_count = len(results['results'])
        logger.info(f"🔍 Filtered results: {filtered_count}/{original_count} resumes with score >= {min_score}")

    # Export results to JSON
    exporter = ExportUtils()
    export_result = exporter.export_web_json(results)

    if export_result['success']:
        logger.info(f"💾 Results exported to: {export_result['file_path']}")
    else:
        logger.error(f"❌ Export failed: {export_result['error']}")

    # Display summary
    stats = results['statistics']
    logger.info("\n📈 PROCESSING SUMMARY:")
    logger.info(f"   Total resumes: {stats['total_resumes']}")
    logger.info(f"   Successful: {stats['successful']}")
    logger.info(f"   Failed: {stats['failed']}")
    logger.info(f"   Average score: {stats['average_score']:.2f}")

    logger.info("\n🎯 RECOMMENDATIONS:")
    recs = stats['recommendations']
    logger.info(f"   HIRE: {recs['HIRE']}")
    logger.info(f"   CONSIDER: {recs['CONSIDER']}")
    logger.info(f"   REJECT: {recs['REJECT']}")

    logger.info("\n✅ Processing completed successfully!")

    return results


def process_single_resume(resume_file_path: str, job_description: str,
                         output_dir: str = None, verbose: bool = False) -> Dict[str, Any]:
    """
    Process and score a single resume against a job description

    Args:
        resume_file_path (str): Path to the resume file
        job_description (str): Job description to match against
        output_dir (str): Output directory for results (optional)
        verbose (bool): Enable verbose logging

    Returns:
        Dict containing analysis results
    """

    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Ensure directories exist
    create_directories()

    # Validate inputs
    if not job_description.strip():
        raise ValueError("Job description cannot be empty")

    if not Path(resume_file_path).exists():
        raise ValueError(f"Resume file does not exist: {resume_file_path}")

    # Setup output directory
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
    else:
        output_dir = OUTPUT_DIR
        Path(output_dir).mkdir(exist_ok=True)

    logger.info(f"🚀 Scoring resume: {Path(resume_file_path).name}")
    logger.info(f"📄 Job description: {job_description[:100]}...")

    # Process single resume using ResumeAnalyzer
    analyzer = ResumeAnalyzer()
    result = analyzer.analyze_single_resume(resume_file_path, job_description)

    if result['success']:
        score = result['final_score']
        recommendation = result['recommendation']
        logger.info(f"✅ Score: {score:.1f} - Recommendation: {recommendation}")

        # Export result to JSON
        exporter = ExportUtils()

        # Create a results structure for export
        export_data = {
            'success': True,
            'results': [result],
            'statistics': {
                'total_resumes': 1,
                'successful': 1,
                'failed': 0,
                'average_score': score,
                'recommendations': {recommendation: 1, 'HIRE': 0, 'CONSIDER': 0, 'REJECT': 0}
            },
            'job_description': job_description
        }
        export_data['statistics']['recommendations'][recommendation] = 1

        export_result = exporter.export_web_json(export_data)

        if export_result['success']:
            logger.info(f"💾 Result exported to: {export_result['file_path']}")
        else:
            logger.error(f"❌ Export failed: {export_result['error']}")

        logger.info("✅ Scoring completed successfully!")
    else:
        error = result.get('error', 'Unknown error')
        logger.error(f"❌ Scoring failed: {error}")

    return result


def setup_environment() -> bool:
    """
    Setup the resume parser environment and test Ollama connection

    Returns:
        bool: True if setup successful, False otherwise
    """

    logger.info("🔧 Setting up Resume Parser environment...")

    try:
        # Create directories
        create_directories()
        logger.info(f"✅ Created directories: {RESUMES_DIR}, {OUTPUT_DIR}")

        # Test Ollama connection
        from ollama_client import OllamaClient
        client = OllamaClient()
        logger.info("✅ Ollama connection successful")

        # Test with a simple prompt
        test_result = client.generate_response("Hello, are you working?")
        if test_result['success']:
            logger.info("✅ Ollama model test successful")
            logger.info("✅ Setup completed successfully!")
            return True
        else:
            logger.error(f"⚠️  Ollama model test failed: {test_result['error']}")
            return False

    except Exception as e:
        logger.error(f"❌ Setup failed: {str(e)}")
        logger.error("Make sure Ollama is running: 'ollama serve'")
        logger.error("And the model is available: 'ollama pull llama3.2:3b'")
        return False


# Example usage functions
def example_usage():
    """Example of how to use the main functions"""

    # Example job description
    job_description = """
    Software Developer Position
    Required Skills: Python, JavaScript, React, SQL, AWS
    Experience: 2-5 years in software development
    Education: Bachelor's degree in Computer Science preferred
    Must have experience with Agile methodologies
    """

    # Example 1: Process all resumes in a directory
    print("Example 1: Processing directory of resumes")
    try:
        results = process_directory(
            resumes_dir="resumes",  # Your resumes directory
            job_description=job_description,
            min_score=60,  # Only include resumes with score >= 60
            verbose=True
        )
        print(f"Processed {results['statistics']['total_resumes']} resumes")
        print(f"Average score: {results['statistics']['average_score']}")
    except Exception as e:
        print(f"Error processing directory: {e}")

    # Example 2: Process a single resume
    print("\nExample 2: Processing single resume")
    try:
        result = process_single_resume(
            resume_file_path="resumes/john_doe.pdf",  # Path to specific resume
            job_description=job_description,
            verbose=True
        )
        if result['success']:
            print(f"Score: {result['final_score']}")
            print(f"Recommendation: {result['recommendation']}")
        else:
            print(f"Error: {result['error']}")
    except Exception as e:
        print(f"Error processing single resume: {e}")


if __name__ == '__main__':
    # Setup environment first
    # if setup_environment():
    #     print("\n" + "="*50)
    #     print("Resume Parser is ready to use!")
    #     print("="*50)
    #     print("\nAvailable functions:")
    #     print("1. process_directory(resumes_dir, job_description, ...)")
    #     print("2. process_single_resume(resume_file_path, job_description, ...)")
    #     print("3. setup_environment()")
    #     print("\nRun example_usage() to see examples")
    # else:
    #     print("Setup failed. Please check Ollama installation and configuration.")
    
    DEFAULT_JOB_DESCRIPTION = """
        Python & MySQL Developer - Fresher
        📍 Location: Vijayawada, India
        🕒 Job Type: Full-Time | Entry-Level
        🌟 About the Role
        We're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!

        🚀 Key Responsibilities
        Build and manage backend logic using Python with a focus on clean data structures

        Write efficient queries to interact with MySQL databases for CRUD operations

        Collaborate with frontend developers to integrate APIs and ensure seamless data flow

        Debug and optimize backend code for performance and scalability

        Document processes and assist in deployment pipelines

        🧠 Required Skills
        Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts

        Familiarity with basic MySQL queries, joins, and indexing

        Exposure to version control systems like Git

        Good problem-solving and algorithmic thinking


        🤝 What We Offer
        Mentorship from senior developers

        Real-world projects to build your portfolio

        A friendly, collaborative work culture

        Opportunities for growth into full-stack development roles
        """
    
    process_single_resume("C:\\Users\\<USER>\\OneDrive\\Desktop\\resume_sasidhar.pdf",DEFAULT_JOB_DESCRIPTION)
